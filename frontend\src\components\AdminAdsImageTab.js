// AdminAdsImageTab.js
import React, { useState, useEffect } from 'react';
import { Upload, Edit, Trash2 } from 'lucide-react';
import axios from 'axios';
import { toast } from 'react-hot-toast';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8001';
const API = `${BACKEND_URL}/api`;

const AdminAdsImageTab = () => {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [form, setForm] = useState({
    file: null,
  });
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    fetchImages();
  }, []);

  const fetchImages = async () => {
    setLoading(true);
    try {
      const res = await axios.get(`${API}/adimages`);
      setImages(res.data);
    } catch {
      toast.error('Failed to fetch images');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, files } = e.target;
    const file = files ? files[0] : null;

    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('Please select a valid image file (JPG, PNG, GIF, or WebP)');
        e.target.value = '';
        return;
      }

      // Validate file size (10MB limit)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        toast.error('File size must be less than 10MB');
        e.target.value = '';
        return;
      }
    }

    setForm((prev) => ({
      ...prev,
      [name]: file,
    }));
  };

  const handleUpload = async (e) => {
    e.preventDefault();
    if (!form.file) {
      toast.error('Please select an image file');
      return;
    }
    setUploading(true);
    setProgress(0);
    const data = new FormData();
    data.append('file', form.file);

    try {
      await axios.post(`${API}/adimages/upload`, data, {
        headers: { 'Content-Type': 'multipart/form-data' },
        onUploadProgress: (evt) => {
          setProgress(Math.round((evt.loaded * 100) / evt.total));
        },
      });
      toast.success('Image uploaded successfully');
      setForm({ file: null });
      setProgress(0);
      fetchImages();
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 'Upload failed';
      toast.error(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  const handleDelete = async (id) => {
    if (!window.confirm('Delete this image?')) return;
    try {
      await axios.delete(`${API}/adimages/${id}`);
      toast.success('Image deleted successfully');
      fetchImages();
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 'Delete failed';
      toast.error(errorMessage);
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
        <Upload className="w-5 h-5" /> Ad Image Management
      </h2>
      <form onSubmit={handleUpload} className="bg-white rounded-lg shadow p-4 mb-6 space-y-4">
        <div>
          <label className="block mb-1 font-medium">Image File (will be saved to <b>/uploads/adimages/</b>)</label>
          <input
            type="file"
            name="file"
            accept=".jpg,.jpeg,.png,.gif,.webp"
            onChange={handleInputChange}
            required
            className="w-full border px-2 py-1 rounded"
          />
          <p className="text-sm text-gray-500 mt-1">
            Supported formats: JPG, JPEG, PNG, GIF, WebP (Max size: 10MB)
          </p>
        </div>
        <button type="submit" disabled={uploading} className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 disabled:opacity-50">
          {uploading ? 'Uploading...' : 'Upload Image'}
        </button>
        {uploading && (
          <div className="w-full bg-gray-200 rounded h-2 mt-2">
            <div className="bg-purple-500 h-2 rounded transition-all duration-300" style={{ width: `${progress}%` }} />
          </div>
        )}
      </form>
      <h3 className="text-lg font-semibold mb-4">Uploaded Images</h3>
      {loading ? (
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
          <p className="mt-2 text-gray-600">Loading images...</p>
        </div>
      ) : images.length === 0 ? (
        <div className="text-center py-8 bg-white rounded-lg shadow">
          <Upload className="mx-auto h-12 w-12 text-gray-400" />
          <p className="mt-2 text-gray-500">No images uploaded yet.</p>
          <p className="text-sm text-gray-400">Upload your first ad image using the form above.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {images.map((image) => (
            <div key={image.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="aspect-w-16 aspect-h-9 bg-gray-200">
                <img
                  src={`${BACKEND_URL}/${image.file_path}`}
                  alt={image.original_filename}
                  className="w-full h-48 object-cover"
                  onError={(e) => {
                    e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4=';
                  }}
                />
              </div>
              <div className="p-4">
                <h4 className="font-medium text-gray-900 truncate" title={image.original_filename}>
                  {image.original_filename}
                </h4>
                <div className="mt-2 space-y-1 text-sm text-gray-500">
                  <p>Size: {(image.file_size / 1024 / 1024).toFixed(2)} MB</p>
                  <p>Type: {image.mime_type}</p>
                  <p>Uploaded: {new Date(image.upload_timestamp).toLocaleDateString()}</p>
                </div>
                <div className="mt-4 flex justify-between items-center">
                  <a
                    href={`${BACKEND_URL}/${image.file_path}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    View Full Size
                  </a>
                  <button
                    onClick={() => handleDelete(image.id)}
                    className="text-red-600 hover:text-red-800 p-1 rounded"
                    title="Delete image"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AdminAdsImageTab;