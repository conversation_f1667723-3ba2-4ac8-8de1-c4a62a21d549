// HomeImageModal.js - Image modal for home page that fetches from API
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { X, ChevronLeft, ChevronRight, Info, Play, Pause } from 'lucide-react';
import axios from 'axios';
import './ImageModal.css';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8001';
const API = `${BACKEND_URL}/api`;

const HomeImageModal = ({ show, onClose }) => {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isAutoSliding, setIsAutoSliding] = useState(true);
  const [showMetadata, setShowMetadata] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [imageLoading, setImageLoading] = useState(false);
  const [imageTransition, setImageTransition] = useState(false);
  
  // Refs for cleanup
  const autoSlideInterval = useRef(null);
  const pauseTimeout = useRef(null);

  // Fetch images from API (public endpoint - no auth required)
  const fetchImages = async () => {
    setLoading(true);
    try {
      // Try to fetch images without authentication first (for public access)
      const response = await axios.get(`${API}/adimages/public`);
      setImages(response.data);
    } catch (error) {
      // If public endpoint doesn't exist, try with potential auth
      try {
        const response = await axios.get(`${API}/adimages`);
        setImages(response.data);
      } catch (authError) {
        console.log('No images available or authentication required');
        setImages([]);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (show) {
      fetchImages();
    }
  }, [show]);

  // Modal helper functions
  const closeModal = () => {
    setIsAutoSliding(false);
    clearInterval(autoSlideInterval.current);
    clearTimeout(pauseTimeout.current);
    onClose();
  };

  const nextImage = useCallback(() => {
    if (images.length > 0) {
      setImageTransition(true);
      setTimeout(() => {
        setCurrentImageIndex((prev) => (prev + 1) % images.length);
        setImageTransition(false);
      }, 150);
    }
  }, [images.length]);

  const prevImage = useCallback(() => {
    if (images.length > 0) {
      setImageTransition(true);
      setTimeout(() => {
        setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
        setImageTransition(false);
      }, 150);
    }
  }, [images.length]);

  const goToImage = (index) => {
    setCurrentImageIndex(index);
    pauseAutoSlide();
  };

  const pauseAutoSlide = () => {
    setIsPaused(true);
    clearTimeout(pauseTimeout.current);
    pauseTimeout.current = setTimeout(() => {
      setIsPaused(false);
    }, 3000); // Resume after 3 seconds of no interaction
  };

  const toggleAutoSlide = () => {
    setIsAutoSliding(!isAutoSliding);
    if (isAutoSliding) {
      clearInterval(autoSlideInterval.current);
    }
  };

  // Auto-slide effect
  useEffect(() => {
    if (show && isAutoSliding && !isPaused && images.length > 1) {
      autoSlideInterval.current = setInterval(() => {
        nextImage();
      }, 5000); // 5 seconds interval for home page
    } else {
      clearInterval(autoSlideInterval.current);
    }

    return () => clearInterval(autoSlideInterval.current);
  }, [show, isAutoSliding, isPaused, images.length, nextImage]);

  // Keyboard navigation effect
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (!show) return;
      
      switch (e.key) {
        case 'Escape':
          closeModal();
          break;
        case 'ArrowLeft':
          prevImage();
          pauseAutoSlide();
          break;
        case 'ArrowRight':
          nextImage();
          pauseAutoSlide();
          break;
        case ' ': // Spacebar
          e.preventDefault();
          toggleAutoSlide();
          break;
        case 'i':
        case 'I':
          setShowMetadata(!showMetadata);
          break;
        default:
          break;
      }
    };

    if (show) {
      document.addEventListener('keydown', handleKeyPress);
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }

    return () => {
      document.removeEventListener('keydown', handleKeyPress);
      document.body.style.overflow = 'unset';
    };
  }, [show, nextImage, prevImage, showMetadata]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearInterval(autoSlideInterval.current);
      clearTimeout(pauseTimeout.current);
      document.body.style.overflow = 'unset';
    };
  }, []);

  // Don't render if not showing or no images
  if (!show) return null;

  // Loading state
  if (loading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center animate-fadeIn">
        <div 
          className="absolute inset-0 bg-black bg-opacity-90 transition-opacity duration-300 backdrop-blur-sm"
          onClick={closeModal}
        />
        <div className="relative text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p>Loading images...</p>
        </div>
      </div>
    );
  }

  // No images state
  if (images.length === 0) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center animate-fadeIn">
        <div 
          className="absolute inset-0 bg-black bg-opacity-90 transition-opacity duration-300 backdrop-blur-sm"
          onClick={closeModal}
        />
        <div className="relative text-white text-center p-8">
          <h3 className="text-xl font-semibold mb-4">No Images Available</h3>
          <p className="text-gray-300 mb-6">There are currently no advertisement images to display.</p>
          <button
            onClick={closeModal}
            className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center animate-fadeIn">
      {/* Overlay */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-90 transition-opacity duration-300 backdrop-blur-sm"
        onClick={closeModal}
      />
      
      {/* Modal Content */}
      <div className="relative w-full h-full flex flex-col">
        {/* Header */}
        <div className="absolute top-0 left-0 right-0 z-10 flex justify-between items-center p-4 modal-header">
          <div className="flex items-center space-x-2 md:space-x-4 modal-controls">
            {images.length > 1 && (
              <button
                onClick={toggleAutoSlide}
                className="modal-button text-white hover:text-gray-300 transition-colors p-2 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70 focus:outline-none focus:ring-2 focus:ring-white"
                title={isAutoSliding ? "Pause slideshow (Space)" : "Start slideshow (Space)"}
              >
                {isAutoSliding ? <Pause className="w-4 h-4 md:w-5 md:h-5" /> : <Play className="w-4 h-4 md:w-5 md:h-5" />}
              </button>
            )}
            <button
              onClick={() => setShowMetadata(!showMetadata)}
              className="modal-button text-white hover:text-gray-300 transition-colors p-2 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70 focus:outline-none focus:ring-2 focus:ring-white"
              title="Toggle image info (I)"
            >
              <Info className="w-4 h-4 md:w-5 md:h-5" />
            </button>
          </div>
          <button
            onClick={closeModal}
            className="modal-button text-white hover:text-gray-300 transition-colors p-2 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70 focus:outline-none focus:ring-2 focus:ring-white"
            title="Close (Esc)"
          >
            <X className="w-5 h-5 md:w-6 md:h-6" />
          </button>
        </div>

        {/* Navigation Arrows */}
        {images.length > 1 && (
          <>
            <button
              onClick={() => { prevImage(); pauseAutoSlide(); }}
              className="navigation-arrow absolute left-2 md:left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 transition-all duration-200 p-2 md:p-3 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70 hover:scale-110 z-10 focus:outline-none focus:ring-2 focus:ring-white"
              title="Previous image (←)"
            >
              <ChevronLeft className="w-6 h-6 md:w-8 md:h-8" />
            </button>
            <button
              onClick={() => { nextImage(); pauseAutoSlide(); }}
              className="navigation-arrow absolute right-2 md:right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 transition-all duration-200 p-2 md:p-3 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70 hover:scale-110 z-10 focus:outline-none focus:ring-2 focus:ring-white"
              title="Next image (→)"
            >
              <ChevronRight className="w-6 h-6 md:w-8 md:h-8" />
            </button>
          </>
        )}

        {/* Main Image */}
        <div className="flex-1 flex items-center justify-center p-4 pt-20 pb-32 modal-content">
          <div className="relative max-w-full max-h-full animate-slideIn">
            <img
              src={`${BACKEND_URL}/${images[currentImageIndex].file_path}`}
              alt={images[currentImageIndex].original_filename}
              className={`max-w-full max-h-full object-contain rounded-lg shadow-2xl image-transition ${
                imageTransition ? 'image-loading' : ''
              }`}
              style={{ maxHeight: 'calc(100vh - 200px)' }}
              onLoad={() => setImageLoading(false)}
              onLoadStart={() => setImageLoading(true)}
            />
            
            {/* Metadata Overlay */}
            {showMetadata && (
              <div className="metadata-overlay absolute bottom-4 left-2 right-2 md:left-4 md:right-4 bg-black bg-opacity-80 backdrop-blur-sm text-white p-3 md:p-4 rounded-lg transition-all duration-300 animate-slideIn">
                <h3 className="font-semibold text-base md:text-lg mb-2 truncate" title={images[currentImageIndex].original_filename}>
                  {images[currentImageIndex].original_filename}
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 text-xs md:text-sm">
                  <div className="flex flex-col sm:flex-row sm:items-center">
                    <span className="text-gray-300 font-medium">Size:</span>
                    <span className="sm:ml-1">{(images[currentImageIndex].file_size / 1024 / 1024).toFixed(2)} MB</span>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:items-center">
                    <span className="text-gray-300 font-medium">Type:</span>
                    <span className="sm:ml-1">{images[currentImageIndex].mime_type}</span>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:items-center">
                    <span className="text-gray-300 font-medium">Uploaded:</span>
                    <span className="sm:ml-1">{new Date(images[currentImageIndex].upload_timestamp).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Bottom Controls */}
        <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black to-transparent">
          {/* Dot Indicators */}
          {images.length > 1 && (
            <div className="flex justify-center mb-4">
              <div className="flex space-x-2">
                {images.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToImage(index)}
                    className={`dot-indicator dot-button w-2 h-2 md:w-3 md:h-3 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white ${
                      index === currentImageIndex 
                        ? 'bg-white scale-125 shadow-lg' 
                        : 'bg-white bg-opacity-50 hover:bg-opacity-75 hover:scale-110'
                    }`}
                    title={`Go to image ${index + 1}`}
                    aria-label={`Go to image ${index + 1}`}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Thumbnail Strip */}
          {images.length > 1 && (
            <div className="flex justify-center">
              <div className="thumbnail-strip flex space-x-2 overflow-x-auto max-w-full pb-2 px-4">
                {images.map((image, index) => (
                  <button
                    key={image.id}
                    onClick={() => goToImage(index)}
                    className={`thumbnail-item thumbnail-button flex-shrink-0 w-12 h-9 md:w-16 md:h-12 rounded overflow-hidden transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white ${
                      index === currentImageIndex 
                        ? 'ring-2 ring-white scale-110 shadow-lg' 
                        : 'opacity-70 hover:opacity-100 hover:scale-105'
                    }`}
                    title={image.original_filename}
                    aria-label={`View ${image.original_filename}`}
                  >
                    <img
                      src={`${BACKEND_URL}/${image.file_path}`}
                      alt={image.original_filename}
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HomeImageModal;
